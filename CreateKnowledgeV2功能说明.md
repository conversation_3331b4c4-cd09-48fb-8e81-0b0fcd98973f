# 知识创建V2功能说明

## 功能概述

知识创建V2是一个支持使用多个PPT文档片段组合成完整PPT文件的功能模块。用户可以通过选择业务模板、PPT模板和知识片段，自动生成完整的PPT文档。

## 核心概念

### 1. 业务类型和知识信息
- **KnoBusinessType**: 业务类型，用于分类管理知识
- **KnoBase**: 知识信息，包含标题、描述、分类、部门、标签、文档类型、业务类型等字段
- **KnoBaseDetail**: 知识明细信息，包含每页的详细内容

### 2. PPT模板系统
- **PptTemplate**: PPT主题模板信息
- **PptTemplateLayout**: PPT模板布局信息，一个主题模板对应多个布局信息

### 3. 业务模板系统
- **KnoBusinessTemplate**: 业务模板
- **KnoBusinessTemplateType**: 业务模板明细，一个业务模板对应多个业务模板明细

### 4. 知识片段
- **知识片段**: KnoBase中documentType字段值为"2"的知识信息
- **业务类型关联**: 通过knoBusinessTypeId字段关联业务类型

## 功能流程

### 1. 基本信息维护
用户可以维护KnoBase基本信息：
- 标题
- 描述
- 分类
- 所属部门
- 标签
- 文档类型
- 业务类型

### 2. 业务模板选择
- 展示业务模板列表
- 选择业务模板后，展示对应的业务类型列表
- 点击业务类型可以查询对应的知识片段列表
- 每个业务类型有默认选择的知识片段，可以更改选择

### 3. PPT模板选择
- 卡片展示PPT模板，可以查看每个布局的图片信息
- 参考AiGenerate3.vue中的选择PPT主题模块

### 4. PPT文件生成
根据选择的知识片段列表和PPT模板信息生成新的PPT文件：

1. **主题页面生成**
   - 根据PPT模板信息找到layoutType为"topic"的布局模板页面
   - 将KnoBase基本信息中的title、content数据套入布局模板
   - 生成主题页面，放入新PPT第一页

2. **目录页生成**
   - 根据PPT模板信息找到layoutType为"directory"的布局模板页面
   - 把业务类型列表中的name作为目录页数据套入布局模板
   - 生成目录页面，放入新PPT第二页

3. **内容页生成**
   - 遍历知识片段列表
   - 找到知识片段中的PPT文件
   - 将PPT文件中的每一页放入新PPT中

4. **结束页生成**
   - 根据PPT模板信息找到layoutType为"end"的布局模板页面
   - 生成结束页，放入新PPT结尾

5. **文件保存**
   - 保存新PPT文件到磁盘路径

### 5. 知识信息保存
- 保存KnoBase信息
- 解析新PPT文件生成KnoBaseDetail信息

## 技术实现

### 前端实现
- **页面**: `kmp-ui-vue/src/views/createknowledge2/CreateKnowledge2.vue`
- **服务**: `kmp-ui-vue/src/service/createKnowledge2.js`
- **路由**: 已添加到路由配置中

### 后端实现
- **API**: `km-biz/src/main/java/org/jeecg/modules/km/kno/api/CreateKnowledgeV2Api.java`
- **服务**: `km-biz/src/main/java/org/jeecg/modules/km/kno/service/CreateKnowledgeServiceV2.java`

### 核心方法
1. `getBusinessTemplateList()` - 获取业务模板列表
2. `getBusinessTypesByTemplate()` - 根据业务模板获取业务类型列表
3. `getKnowledgeFragmentsByBusinessType()` - 根据业务类型获取知识片段列表
4. `getPptTemplateList()` - 获取PPT模板列表
5. `combinePptFragments()` - 组合PPT片段生成完整PPT
6. `saveKnowledgeAndParsePpt()` - 保存知识信息并解析PPT

## 使用说明

### 1. 访问页面
在首页点击"创建知识V2"按钮，或直接访问 `/create-knowledge2` 路径。

### 2. 填写基本信息
- 输入知识标题（必填）
- 输入知识描述
- 选择知识分类
- 选择所属部门
- 输入关键词

### 3. 选择业务模板
- 从下拉列表中选择业务模板
- 系统会显示该模板对应的业务类型列表
- 点击业务类型可以查看和选择知识片段

### 4. 选择PPT模板
- 从下拉列表中选择PPT模板
- 可以预览模板的各个布局页面

### 5. 选择知识片段
- 为每个业务类型选择对应的知识片段
- 可以查看片段详情和知识详情

### 6. 生成PPT
- 点击"生成PPT"按钮
- 系统会根据选择的信息生成完整的PPT文件

### 7. 保存知识
- 点击"保存知识"按钮
- 系统会保存知识信息并解析PPT生成明细

## 注意事项

1. **文件路径**: 确保PPT模板文件和知识片段文件路径正确
2. **权限控制**: 只有管理员用户可以使用此功能
3. **数据完整性**: 确保业务模板、PPT模板和知识片段数据完整
4. **文件格式**: 支持.pptx格式的PPT文件

## 错误处理

- 如果PPT模板文件不存在，会提示"PPT模板文件不存在"
- 如果知识片段文件不存在，会跳过该片段继续处理
- 如果生成过程中出现异常，会回滚事务并提示错误信息

## 扩展功能

1. **批量处理**: 支持批量选择多个知识片段
2. **模板预览**: 提供更详细的PPT模板预览功能
3. **自定义布局**: 支持用户自定义PPT布局
4. **版本管理**: 支持PPT模板和知识片段的版本管理 