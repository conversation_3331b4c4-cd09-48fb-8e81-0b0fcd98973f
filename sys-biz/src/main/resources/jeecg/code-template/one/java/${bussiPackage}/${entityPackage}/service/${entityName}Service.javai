package ${bussiPackage}.${entityPackage}.service;

import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${bussiPackage}.${entityPackage}.entity.${entityName};
import ${bussiPackage}.${entityPackage}.mapper.${entityName}Mapper;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import cn.hutool.core.util.StrUtil;

import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Map;
import java.util.List;

@Service
//${tableVo.ftlDescription}
public class ${entityName}Service {

    @Autowired
    private ${entityName}Mapper ${entityName?uncap_first}Mapper;


	// 添加${tableVo.ftlDescription}
	public ${entityName} saveOrUpdate${entityName}(${entityName} ${entityName?uncap_first}) {
	    if(StrUtil.isEmpty(${entityName?uncap_first}.getId())){
	    	  ${entityName?uncap_first}Mapper.insert(${entityName?uncap_first});
	    }else{
	          ${entityName?uncap_first}Mapper.updateById(${entityName?uncap_first});
	    }
		return ${entityName?uncap_first};
	}



	// 删除${tableVo.ftlDescription}
	public void delete${entityName}(String id) {
		 ${entityName?uncap_first}Mapper.deleteById(id);
	}


	// 删除${tableVo.ftlDescription}
	public void delete${entityName}ByIds(String ids) {
	    List<String> idsList = StrUtil.split(ids, ',');
	    for(String id : idsList){
	    	 delete${entityName}(id);
	    }
	}



	// 查询${tableVo.ftlDescription}
	public ${entityName} find${entityName}ById(String id) {
		${entityName} ${entityName?uncap_first} = ${entityName?uncap_first}Mapper.selectById(id);
		return ${entityName?uncap_first};
	}


	// 分页查询${tableVo.ftlDescription}
	public IPage<${entityName}> find${entityName}ByPage(${entityName} ${entityName?uncap_first}, Integer pageNo,
	    Integer pageSize,Map<String, String[]> parameterMap) {
			QueryWrapper<${entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${entityName?uncap_first},parameterMap);
        	Page<${entityName}> page = new Page<${entityName}>(pageNo, pageSize);
        	IPage<${entityName}> pageList = ${entityName?uncap_first}Mapper.selectPage(page, queryWrapper);
            return pageList;
	}



    // 分页查询${tableVo.ftlDescription} Controller 构建条件
    public IPage<${entityName}> find${entityName}s(Page<${entityName}> page, QueryWrapper<${entityName}> queryWrapper) {
        return ${entityName?uncap_first}Mapper.selectPage(page, queryWrapper);
    }
}
