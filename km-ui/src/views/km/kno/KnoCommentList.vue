<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection">
     <!--插槽:table标题-->
          <template #tableTitle>
             <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
             <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
             <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
             <a-dropdown v-if="selectedRowKeys.length > 0">
                 <template #overlay>
                    <a-menu>
                      <a-menu-item key="1" @click="batchHandleDelete">
                        <Icon icon="ant-design:delete-outlined"></Icon>
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button>批量操作
                    <Icon icon="mdi:chevron-down"></Icon>
                  </a-button>
            </a-dropdown>
          </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <KnoCommentModal @register="registerModal" @success="handleSuccess"></KnoCommentModal>

        <!-- 知识选择弹窗 -->
    <KnoBaseSelectModal 
      v-model:visible="showSelectModal"
      @select="handleSelect"
    />

  </div>
</template>

<script lang="ts" name="kno-knoComment" setup>
  import {ref, computed, unref, onMounted, onUnmounted} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import KnoCommentModal from './modules/KnoCommentModal.vue'
  import {columns, searchFormSchema} from './KnoComment.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './KnoComment.api';
  import KnoBaseSelectModal from './modules/KnoBaseSelectModal.vue';

  //[选择知识弹窗使用] 控制 选择知识 弹窗显示
  const showSelectModal = ref(false);
  //[选择知识弹窗使用] 处理 选择知识 按钮点击
  const handleSelectClick = () => {
    console.log('触发选择弹窗');
    showSelectModal.value = true;
  };
  //[选择知识弹窗使用] 监听 选择知识弹框 事件 点击选择按钮时触发
  onMounted(() => {
    window.addEventListener('openSelectModal', handleSelectClick);
  });
  //[选择知识弹窗使用] 卸载 选择知识弹框 事件 点击选择按钮时触发
  onUnmounted(() => {
    window.removeEventListener('openSelectModal', handleSelectClick);
  });

  /**
  * [选择知识弹窗使用] 在 选择知识弹窗 选择知识的回调
  */
  function handleSelect(record) {
      console.log('选中记录----:', JSON.stringify(record));
      // 更新表单值并触发查询  setFieldsValue 设置表单值  submit 触发查询
      const { setFieldsValue, submit } = getForm();
      setFieldsValue({
        knowledgeId: record.id,
        knowledgeTitle: record.title
      });
      // 触发查询
      submit();
  }


  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
       tableProps:{
            title: '知识评论',
            api: list,
            columns,
            canResize:false,
            formConfig: {
               labelWidth: 120,
               schemas: searchFormSchema,
               autoSubmitOnEnter:true,
               showAdvancedButton:true,
             },
            actionColumn: {
                width: 120,
             },
        },
        exportConfig: {
             name:"知识评论",
             url: getExportUrl,
           },
           importConfig: {
             url: getImportUrl
           },
   })

   const [registerTable, {reload, getForm},{ rowSelection, selectedRowKeys }] = tableContext

   /**
    * 新增事件
    */
   function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
   }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
   async function handleDelete(record) {
     await deleteOne({id: record.id}, reload);
   }
   /**
    * 批量删除事件
    */
   async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, reload);
   }
   /**
    * 成功回调
    */
   function handleSuccess({isUpdate, values}) {
      reload();
   }
   /**
      * 操作栏
      */
   function getTableAction(record){
       return [
          {
             label: '详情',
             onClick: handleDetail.bind(null, record),
           }
       ]
     }
     /**
       * 下拉操作栏
       */
    function getDropDownAction(record){
      return [
           {
             label: '详情',
             onClick: handleDetail.bind(null, record),
           }, {
             label: '删除',
             popConfirm: {
               title: '是否确认删除',
               confirm: handleDelete.bind(null, record),
             }
           }
      ]
    }
</script>
<style scoped>

</style>