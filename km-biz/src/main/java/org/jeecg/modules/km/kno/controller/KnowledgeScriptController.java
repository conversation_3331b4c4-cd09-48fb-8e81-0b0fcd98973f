package org.jeecg.modules.km.kno.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.km.kno.service.VectorStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 向量存储控制器
 * 提供向量服务的代理API
 * yf add 2025-01-13
 */
@Api(tags = "向量存储管理")
@RestController
@RequestMapping("/kno/vector")
@Slf4j
public class VectorController {

    @Autowired
    private VectorStoreService vectorStoreService;

    /**
     * 根据知识库ID同步文档到向量库
     * @param knowledgeId 知识库ID
     * @return 同步结果
     */
    @ApiOperation(value = "根据知识库ID同步文档", notes = "将指定知识库的文档同步到向量库")
    @PostMapping("/sync-by-knowledge-id")
    public Result<Map<String, Object>> syncDocumentsByKnowledgeId(
            @ApiParam(value = "知识库ID", required = true) 
            @RequestParam String knowledgeId) {
        
        try {
            log.info("接收到同步知识库文档请求，知识库ID: {}", knowledgeId);
            
            if (knowledgeId == null || knowledgeId.trim().isEmpty()) {
                return Result.error("知识库ID不能为空");
            }
            
            Map<String, Object> result = vectorStoreService.syncDocumentsByKnowledgeId(knowledgeId.trim());
            
            // 判断同步结果
            if (result != null && Boolean.TRUE.equals(result.get("success"))) {
                return Result.OK("同步成功", result);
            } else {
                String message = result != null ? (String) result.get("message") : "同步失败";
                return Result.error(message, result);
            }
            
        } catch (Exception e) {
            log.error("同步知识库文档失败，知识库ID: {}", knowledgeId, e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 手动同步所有文档到向量库
     * @return 同步结果
     */
    @ApiOperation(value = "手动同步所有文档", notes = "将所有文档数据同步到向量库")
    @PostMapping("/manual-sync-all")
    public Result<String> manualSyncAllDocuments() {
        
        try {
            log.info("接收到手动同步所有文档请求");
            
            String result = vectorStoreService.manualSyncAllDocuments();
            
            // 判断结果是否包含错误信息
            if (result != null && !result.contains("同步失败")) {
                return Result.OK("同步完成", result);
            } else {
                return Result.error(result != null ? result : "同步失败");
            }
            
        } catch (Exception e) {
            log.error("手动同步所有文档失败", e);
            return Result.error("同步失败: " + e.getMessage());
        }
    }

}
